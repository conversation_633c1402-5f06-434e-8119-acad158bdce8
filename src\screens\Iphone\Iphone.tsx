import React from "react";
import { Card, CardContent } from "../../components/ui/card";

export const Iphone = (): JSX.Element => {
  // Air quality metrics data
  const airQualityMetrics = [
    {
      value: "014",
      label: "PM2.5(µg/m³）",
      width: "w-[73px]",
    },
    {
      value: "36",
      label: "甲醛(µg/m³）",
      width: "w-[65px]",
    },
    {
      value: "25500",
      label: "负氧离子(个/cm³）",
      width: "w-[88px]",
    },
  ];

  return (
    <div
      className="bg-neutral-100 flex flex-row justify-center w-full"
      data-model-id="107:9"
    >
      <div className="bg-neutral-100 w-[393px] h-[852px] relative">
        {/* Status bar */}
        <img
          className="absolute w-[375px] h-11 top-0 left-[11px]"
          alt="Status bar"
          src="https://c.animaapp.com/mdqqarhondzElL/img/---------.svg"
        />

        <div className="absolute w-[393px] h-[783px] top-[69px] left-0">
          {/* Background image */}
          <img
            className="absolute w-[393px] h-[619px] top-[164px] left-0 object-cover"
            alt="Background"
            src="https://c.animaapp.com/mdqqarhondzElL/img/----.png"
          />

          {/* Circular gradient elements */}
          <div className="absolute w-full top-0 left-0">
            {/* Outer circle - custom gradient */}
            <div
              className="absolute w-[311px] h-[311px] top-[18px] left-10 rounded-full"
              style={{
                background: 'radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,200,150,0.3) 40%, rgba(255,150,100,0.4) 70%, rgba(255,100,50,0.2) 100%)'
              }}
            />

            {/* Middle circle - custom gradient */}
            <div
              className="absolute w-[231px] h-[231px] top-[58px] left-[75px] rounded-full"
              style={{
                background: 'radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(200,255,200,0.4) 40%, rgba(150,255,200,0.5) 70%, rgba(100,255,200,0.3) 100%)'
              }}
            />

            {/* Inner circle - custom gradient */}
            <div
              className="absolute w-[175px] h-[175px] top-[86px] left-[121px] rounded-full"
              style={{
                background: 'radial-gradient(circle, rgba(200,255,255,0.3) 0%, rgba(150,255,255,0.5) 40%, rgba(100,255,255,0.6) 70%, rgba(50,200,255,0.4) 100%)'
              }}
            />

            {/* Car visualization */}
            <img
              className="absolute w-[315px] h-[210px] top-[69px] left-[39px] object-cover"
              alt="Car visualization"
              src="https://c.animaapp.com/mdqqarhondzElL/img/---2.png"
            />

            {/* Air quality indicator */}
            <div className="absolute w-[75px] h-24 top-[49px] left-[166px]">
              <div className="absolute w-[69px] h-[87px] top-[9px] left-0">
                <div className="top-0 left-1 text-[#454545] text-xl whitespace-nowrap absolute [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal tracking-[0] leading-[normal]">
                  空气优
                </div>

                <div className="top-[17px] left-0 text-[#1b705f] text-6xl absolute [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal tracking-[0] leading-[normal] whitespace-nowrap">
                  25
                </div>
              </div>

              <div className="top-0 left-2.5 text-[#454545] text-[6px] whitespace-nowrap absolute [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal tracking-[0] leading-[normal]">
                车内综合空气质量
              </div>
            </div>

            {/* Air quality logo */}
            <img
              className="absolute w-[177px] h-[90px] top-[58px] left-[105px]"
              alt="Air quality logo"
              src="https://c.animaapp.com/mdqqarhondzElL/img/group-13.png"
            />

            {/* Mask group overlay */}
            <img
              className="absolute w-[349px] h-[349px] top-0 left-[22px]"
              alt="Mask group"
              src="https://c.animaapp.com/mdqqarhondzElL/img/mask-group.png"
            />
          </div>

          {/* Health protection days */}
          <div className="top-64 left-[109px] text-transparent text-[15px] absolute [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal tracking-[0] leading-[normal]">
            <span className="text-[#5b5b5b]">已为您健康守护 </span>
            <span className="text-[#5b5b5b] text-[19px]">231</span>
            <span className="text-[#5b5b5b]"> 天</span>
          </div>

          {/* Cleaning reminder setting */}
          <div className="top-[282px] left-[149px] text-transparent text-[15px] whitespace-nowrap absolute [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal tracking-[0] leading-[normal]">
            <span className="text-[#5b5b5b]">设置 </span>
            <span className="text-[#ff8800]">清洗提醒</span>
          </div>

          {/* Air quality metrics */}
          <div className="absolute w-[298px] h-[45px] top-80 left-[42px] flex justify-between">
            {airQualityMetrics.map((metric, index) => (
              <div key={index} className={`h-[45px] ${metric.width}`}>
                <div className="relative h-full">
                  <div className="absolute top-0 left-1.5 [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal text-[#494949] text-[28px] tracking-[0] leading-[normal] whitespace-nowrap">
                    {metric.value}
                  </div>
                  <div className="absolute top-[33px] left-0 [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal text-[#909090] text-[10px] tracking-[0] leading-[normal] whitespace-nowrap">
                    {metric.label}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Weather and air quality card */}
          <Card className="absolute h-[183px] top-[409px] left-[23px] w-[345px] border-none shadow-none bg-transparent">
            <CardContent className="p-0">
              <img
                className="w-full h-full"
                alt="Weather and air quality information"
                src="https://c.animaapp.com/mdqqarhondzElL/img/frame-1.svg"
              />
            </CardContent>
          </Card>

          {/* Control panel */}
          <Card className="absolute h-[74px] top-[637px] left-[22px] w-[345px] border-none shadow-none bg-transparent">
            <CardContent className="p-0">
              <img
                className="w-full h-full"
                alt="Control panel"
                src="https://c.animaapp.com/mdqqarhondzElL/img/frame-2.svg"
              />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
